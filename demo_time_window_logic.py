#!/usr/bin/env python3
"""
演示时间窗口逻辑的详细行为

场景：
1. 窗口内持续相同故障 - 只报警一次，直到超窗口
2. 窗口内故障数量变化 - 立即重新报警
3. 窗口内故障恢复再异常 - 重新开始报警
"""

import sys
import logging
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from server.utils.video_processor import VideoFrameProcessor
from server.utils.handlers.filter_handler import FilterHandler

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def create_test_config():
    """创建测试配置"""
    return {
        'filter_alarm_suppression_enabled': True,
        'filter_alarm_suppression_window': 120,  # 2分钟窗口
        'storage': {
            'paths': {
                'base_dataset': 'test_datasets'
            }
        }
    }

def create_response_dict(fault_count):
    """创建检测结果"""
    if fault_count > 0:
        return {
            "曝气头是否脱落或损坏": "是",
            "故障曝气头数量": fault_count,
            "故障设备详情": [{"id": i, "status": "malfunction"} for i in range(fault_count)]
        }
    else:
        return {
            "曝气头是否脱落或损坏": "否",
            "故障曝气头数量": 0,
            "故障设备详情": []
        }

def test_scenario(handler, camera_id, scenario_name, test_steps):
    """测试场景"""
    print(f"\n{'='*60}")
    print(f"🎯 {scenario_name}")
    print(f"{'='*60}")
    
    for step_num, (time_offset, fault_count, expected_alarm, description) in enumerate(test_steps, 1):
        current_time = datetime.now() + timedelta(seconds=time_offset)
        response_dict = create_response_dict(fault_count)
        failure_reasons = ['曝气头可能出现脱落或损坏'] * fault_count if fault_count > 0 else []
        
        detection_result = '是' if fault_count > 0 else '否'
        
        alarm_status, is_abnormal = handler._handle_filter_alarm_suppression(
            camera_id, detection_result, 99 if fault_count > 0 else 1, 50, 
            current_time, failure_reasons, response_dict
        )
        
        # 获取状态信息
        state = handler.processor.filter_fault_state.get(camera_id, {})
        
        print(f"\n📍 步骤{step_num}: {description}")
        print(f"   时间偏移: +{time_offset}秒")
        print(f"   故障数量: {fault_count}")
        print(f"   报警状态: {alarm_status} ({'✅' if alarm_status == 'WARNING' else '❌' if alarm_status == 'NO_ALARM' else '🔄'})")
        print(f"   是否异常: {is_abnormal}")
        print(f"   状态记录: 故障数={state.get('fault_count', 0)}, 已触发={state.get('alarm_triggered', False)}")
        
        # 验证期望结果
        if expected_alarm and alarm_status != 'WARNING':
            print(f"   ⚠️  期望报警但未报警！")
        elif not expected_alarm and alarm_status == 'WARNING':
            print(f"   ⚠️  未期望报警但触发了报警！")
        else:
            print(f"   ✅ 结果符合期望")

def demo_time_window_logic():
    """演示时间窗口逻辑"""
    print("🚀 开始演示时间窗口逻辑...")
    
    config = create_test_config()
    processor = VideoFrameProcessor(config)
    handler = FilterHandler(processor)
    
    # 场景1：窗口内持续相同故障
    test_scenario(handler, "camera_scenario1", "场景1：窗口内持续相同故障", [
        (0,   1, True,  "首次检测到1个故障"),
        (30,  1, False, "30秒后仍是1个故障（窗口内）"),
        (60,  1, False, "60秒后仍是1个故障（窗口内）"),
        (90,  1, False, "90秒后仍是1个故障（窗口内）"),
        (130, 1, True,  "130秒后仍是1个故障（超出窗口）"),
    ])
    
    # 场景2：窗口内故障数量变化
    test_scenario(handler, "camera_scenario2", "场景2：窗口内故障数量变化", [
        (0,  1, True,  "首次检测到1个故障"),
        (30, 1, False, "30秒后仍是1个故障（抑制）"),
        (60, 2, True,  "60秒后变为2个故障（数量变化，重新报警）"),
        (90, 2, False, "90秒后仍是2个故障（新窗口内抑制）"),
        (120, 3, True, "120秒后变为3个故障（数量再次变化）"),
    ])
    
    # 场景3：窗口内故障恢复再异常
    test_scenario(handler, "camera_scenario3", "场景3：窗口内故障恢复再异常", [
        (0,  1, True,  "首次检测到1个故障"),
        (30, 1, False, "30秒后仍是1个故障（抑制）"),
        (60, 0, False, "60秒后故障恢复正常（重置状态）"),
        (90, 1, True,  "90秒后再次出现1个故障（重新开始）"),
        (120, 1, False, "120秒后仍是1个故障（新窗口内抑制）"),
    ])
    
    print(f"\n{'='*60}")
    print("📋 总结：")
    print("1. 相同故障数量在窗口内只报警一次")
    print("2. 故障数量变化立即重新报警（不等窗口结束）")
    print("3. 故障恢复会重置状态，再次异常重新开始计时")
    print("4. 超出窗口时间会重新报警相同故障")
    print(f"{'='*60}")

if __name__ == "__main__":
    try:
        demo_time_window_logic()
    except Exception as e:
        print(f"\n❌ 演示失败: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
